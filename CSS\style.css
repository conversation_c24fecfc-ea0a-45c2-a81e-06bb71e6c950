* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}

:root {
  --bg-color: #1f242d;
  --second-bg-color: #202122;
  --text-color: #fff;
  --main-color: #ac9046;
  --black:#000;
}

/* Logo size */
.logo {
  width: 100px;
  height: 60px;
}

/* Navbar toggler icon */
.nav-bar {
  color: var(--text-color);
  font-size: 1.5rem;
  border-radius: 5px;
}

/* Navbar links */
.navbar-nav .nav-link {
  color: var(--text-color);
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--main-color);
}

.hero .carousel-item img {
  height: 90vh;           /* Full viewport height */
  object-fit: cover;       /* Crop image nicely */
}
.carousel-indicators .active {
  background-color: var(--main-color);
}


#new-arrivals {
  background-color: var(--second-bg-color);
}



/* Equal image height */
.fixed-img {
  width: 100%;
  height: 350px;       /* set your desired height */
  object-fit: cover;   /* crop without distortion */
  border-radius: 8px;  /* optional for rounded corners */
  transition: all 0.6s ease;
}
.fixed-img:hover{
    transform: scale(1.2);
    opacity: 0.5;
    
}

/* To center overlay text */
.card-banner-content {
    bottom: 17px;
    right: 22px;
  color: #fff;
}

.btn{
    color: var(--second-bg-color);
    font-size: 16px;
    padding: 10px 25px;
    border-radius: 5px;
    background-color: var(--main-color);
    border: none;
    cursor: pointer;
}
.btn:hover{
    background-color: transparent;
    border: 2px solid var(--main-color);
    color: var(--main-color);
   
}


.products .nav-item .nav-link{
    color: var(--text-color);
    padding: 10px 20px;
    border: 2px solid var(--text-color);
    border-radius: 20px;
    background-color: var(--bg-color);
    height: 50px;
    margin: 10px 0;
    text-transform: uppercase;
    text-align: center;
    display: block;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-left: 10px;
   
}
.products .nav-item .nav-link:hover,
.products .nav-item .nav-link.active{
    color: var(--main-color);
    border: 2px solid var(--main-color);
    /* background-color: var(--bg-color); */
}


.product-card-img img{
    width: 100%;
    height: 300px;
    object-fit: cover;
    object-position: center;
    border-radius: 8px;
    transition: all 0.6s ease;
    overflow: hidden;
}
.product-card-img img:hover{
    transform: scale(1.2);
    opacity: 0.5;
}

.product-card-content .price{
  margin-bottom: 15px;
}

.product-card-content i{
    color: var(--main-color);
    font-size: 20px;
    transition: all 0.3s ease;
}
.product-card-content i:hover{
    transform: scale(1.2);
    opacity: 0.5;
}

#deals{
    background-color: var(--second-bg-color);
}

#blog{
    background-color: var(--black);
}


.deal-content{
  height: 400px;
  background-image: url("https://images.unsplash.com/photo-1609587312208-cea54be969e7?w=600&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NjR8fHdhdGNofGVufDB8fDB8fHww");
  background-size: cover;
  background-position: center;
  /* border-radius: 8px; */
}

.blog-card img{
    width: 100%;
    height: 300px;
    object-fit: cover;
    object-position: center;
    border-radius: 8px;
    transition: all 0.6s ease;
    overflow: hidden;
}
.blog-card img:hover{
    transform: scale(1.2);
    opacity: 0.5;
}


footer{
    background-color: var(--second-bg-color);
}
.social-icons a{
  text-decoration: none;
}

.social-icons i{
    color: var(--text-color);
    font-size: 30px;
    margin: 0 10px;
    transition: all 0.3s ease;
}
.social-icons i:hover{
    color: var(--main-color);
}